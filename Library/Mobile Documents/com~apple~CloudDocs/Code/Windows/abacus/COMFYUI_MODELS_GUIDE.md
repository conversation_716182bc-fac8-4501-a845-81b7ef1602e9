# ComfyUI Models Organization Guide

Your ComfyUI models are now configured to use your Nextcloud3 directory with the following structure:

## Model Directory Structure

You have three main directories for easy organization:

```
C:\Users\<USER>\Nextcloud3\Documents\ComfyUI\
├── Models/                   # Main models directory (mounted to /root/ComfyUI/models)
│   ├── checkpoints/          # Main Stable Diffusion models (.safetensors, .ckpt)
│   ├── vae/                  # VAE models (.safetensors, .pt)
│   ├── embeddings/           # Textual Inversions (.pt, .safetensors)
│   ├── upscale_models/       # Upscaling models (ESRGAN, Real-ESRGAN)
│   ├── clip/                 # CLIP models
│   ├── unet/                 # U-Net models
│   ├── diffusers/            # Diffusers format models
│   ├── hypernetworks/        # Hypernetwork models
│   ├── photomaker/           # PhotoMaker models
│   ├── insightface/          # InsightFace models
│   ├── facerestore_models/   # Face restoration models
│   ├── sams/                 # Segment Anything models
│   └── configs/              # Model configuration files
├── loras/                    # LoRA models for easy access (mounted directly)
└── controlnet/               # ControlNet models for easy access (mounted directly)
```

**Special Setup**: The `loras` and `controlnet` folders are mounted directly for easier access and organization!

## Creating the Directory Structure

The main folders have been created for you! To create additional subdirectories in the Models folder, run this PowerShell command:

```powershell
$basePath = "C:\Users\<USER>\Nextcloud3\Documents\ComfyUI\Models"
$directories = @(
    "checkpoints", "vae", "embeddings", "upscale_models",
    "clip", "unet", "diffusers", "hypernetworks",
    "photomaker", "insightface", "facerestore_models", "sams",
    "configs"
)

foreach ($dir in $directories) {
    New-Item -ItemType Directory -Path "$basePath\$dir" -Force
}
```

**Note**: The `loras` and `controlnet` folders are already created at the root level for easy access!

## Popular Models to Download

### Essential Checkpoints
- **Stable Diffusion 1.5**: `v1-5-pruned-emaonly.safetensors`
- **Stable Diffusion XL**: `sd_xl_base_1.0.safetensors`
- **Realistic Vision**: `realisticVisionV60B1_v60B1VAE.safetensors`
- **DreamShaper**: `dreamshaper_8.safetensors`

### VAE Models
- **SD 1.5 VAE**: `vae-ft-mse-840000-ema-pruned.safetensors`
- **SDXL VAE**: `sdxl_vae.safetensors`

### ControlNet Models
- **Canny**: `control_v11p_sd15_canny.pth`
- **OpenPose**: `control_v11p_sd15_openpose.pth`
- **Depth**: `control_v11f1p_sd15_depth.pth`

### Upscale Models
- **Real-ESRGAN**: `RealESRGAN_x4plus.pth`
- **ESRGAN**: `ESRGAN_4x.pth`

## Model Sources

### Recommended Sites
1. **Hugging Face**: https://huggingface.co/models
2. **Civitai**: https://civitai.com/
3. **OpenModelDB**: https://openmodeldb.info/

### Download Tips
- Always download `.safetensors` format when available (safer than `.ckpt`)
- Check model requirements and compatibility
- Read model cards for usage instructions
- Verify file integrity after download

## Benefits of Nextcloud3 Integration

✅ **Sync Across Devices**: Models sync automatically via Nextcloud
✅ **Backup**: Models are backed up in your Nextcloud
✅ **Sharing**: Easy to share models with other devices
✅ **Organization**: Keep all AI models in one centralized location
✅ **Version Control**: Nextcloud tracks file changes

## Usage Notes

1. **First Run**: ComfyUI will scan the models directory on startup
2. **New Models**: Restart ComfyUI or refresh the interface after adding new models
3. **Large Files**: Some models are several GB - ensure sufficient storage
4. **Permissions**: Make sure Docker has read access to the Nextcloud directory

## Troubleshooting

### Models Not Appearing
1. Check file permissions on the Nextcloud directory
2. Ensure models are in the correct subdirectories
3. Restart the ComfyUI container: `docker-compose restart comfyui`
4. Check ComfyUI logs: `docker-compose logs comfyui`

### Sync Issues
1. Ensure Nextcloud client is running and syncing
2. Check available disk space
3. Verify network connectivity for large model downloads

### Performance Tips
1. Use SSD storage for better model loading times
2. Keep frequently used models in faster storage
3. Consider model pruning for smaller file sizes
4. Use appropriate precision (fp16 vs fp32) based on your GPU
