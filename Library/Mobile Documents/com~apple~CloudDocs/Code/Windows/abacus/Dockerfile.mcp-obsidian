FROM node:20

# Install Python and uv
RUN apt-get update && \
    apt-get install -y python3 python3-pip curl && \
    pip3 install --break-system-packages uv && \
    npm install -g supergateway && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Set environment variables
ENV OBSIDIAN_VAULT_PATH=/obsidian
ENV PATH=/root/.local/bin:$PATH

# Expose port
EXPOSE 8003

# Run the command
ENTRYPOINT ["/usr/local/bin/npx", "-y", "supergateway", "--stdio", "uvx mcp-obsidian", "--port", "8003"]
