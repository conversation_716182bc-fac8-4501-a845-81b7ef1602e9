FROM vllm/vllm-openai:latest

# Set the working directory
WORKDIR /app

# Set environment variables for Hugging Face cache
ENV HF_HOME=/data/huggingface
ENV TRANSFORMERS_CACHE=/data/huggingface/transformers
ENV HUGGINGFACE_HUB_CACHE=/data/huggingface/hub

# Performance optimization environment variables
ENV TOKENIZERS_PARALLELISM=false
ENV CUDA_DEVICE_ORDER=PCI_BUS_ID
ENV VLLM_WORKER_MULTIPROC_METHOD=spawn
ENV OMP_NUM_THREADS=18
ENV PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:320
ENV CUDA_LAUNCH_BLOCKING=0
ENV TORCH_ALLOW_TF32=1
ENV TORCH_CUDNN_V8_API_ENABLED=1
ENV CUDA_MODULE_LOADING=LAZY

# Copy the startup script
COPY start-vllm.sh /app/start-vllm.sh

# Make the script executable
RUN chmod +x /app/start-vllm.sh

# Set the entrypoint to the startup script
ENTRYPOINT ["/app/start-vllm.sh"]
