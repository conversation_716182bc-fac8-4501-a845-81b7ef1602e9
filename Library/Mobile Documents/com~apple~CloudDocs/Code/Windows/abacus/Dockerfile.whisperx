FROM python:3.11-slim

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    wget \
    curl \
    ffmpeg \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Install PyTorch with GPU support first
RUN pip3 install --no-cache-dir \
    torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Install Python dependencies - using faster-whisper for stability
RUN pip3 install --no-cache-dir \
    faster-whisper \
    flask \
    flask-cors \
    gunicorn \
    requests

# Create directories for models and cache
RUN mkdir -p /app/models /app/cache /root/.cache

# Copy the API server script
COPY <<EOF /app/whisperx_server.py
import os
import tempfile
from faster_whisper import WhisperModel
from flask import Flask, request, jsonify
from flask_cors import CORS
import torch

app = Flask(__name__)
CORS(app)

# Global variables for models - prioritize stability with CPU default
device = os.getenv("WHISPERX_DEVICE", "cpu")  # Default to CPU for stability
compute_type = os.getenv("WHISPERX_COMPUTE_TYPE", "int8")  # Default to CPU optimized
print(f"Using device: {device}")
print(f"CUDA available: {torch.cuda.is_available()}")

# Only configure GPU if we're actually using CUDA
if device == "cuda" and torch.cuda.is_available():
    print(f"CUDA device count: {torch.cuda.device_count()}")
    print(f"CUDA device name: {torch.cuda.get_device_name(0)}")

    # Comprehensive GPU memory management
    torch.cuda.empty_cache()

    # Set ultra-conservative memory fraction (30% of GPU memory)
    torch.cuda.set_per_process_memory_fraction(0.3)

    # Enable memory-efficient attention if available
    try:
        torch.backends.cuda.enable_flash_sdp(True)
    except:
        pass

    # Set memory allocation strategy
    import os
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128,expandable_segments:True'

    print(f"GPU memory fraction set to 30%")
    print(f"GPU memory management configured")
elif device == "cpu":
    print(f"Using CPU mode for maximum stability")
else:
    print(f"CUDA requested but not available, falling back to CPU")
    device = "cpu"
    compute_type = "int8"

model = None

def load_models():
    global model, device, compute_type

    try:
        # Load faster-whisper model with GPU support
        model_name = os.getenv("WHISPERX_MODEL", "base")
        print(f"Loading faster-whisper model: {model_name}")

        # Try GPU first, fallback to CPU if it fails
        try:
            if device == "cuda":
                model = WhisperModel(model_name, device="cuda", compute_type="float16")
                print("GPU model loaded successfully!")
            else:
                model = WhisperModel(model_name, device="cpu", compute_type="int8")
                print("CPU model loaded successfully!")
        except Exception as gpu_error:
            print(f"GPU loading failed: {gpu_error}")
            print("Falling back to CPU mode for stability...")
            model = WhisperModel(model_name, device="cpu", compute_type="int8")
            device = "cpu"  # Update global device setting
            compute_type = "int8"
            print("CPU fallback model loaded successfully!")

        print("Model loaded successfully!")
        return True
    except Exception as e:
        print(f"Error loading model: {e}")
        import traceback
        traceback.print_exc()
        return False

@app.route("/", methods=["GET"])
def health_check():
    return jsonify({"status": "healthy", "service": "WhisperX"})

@app.route("/health", methods=["GET"])
def health():
    return jsonify({"status": "healthy", "service": "WhisperX"})

@app.route("/v1/audio/transcriptions", methods=["POST"])
def transcribe():
    tmp_file_path = None
    try:
        print(f"Received transcription request from {request.remote_addr}")

        # Check if models are loaded
        if model is None:
            return jsonify({"error": "Models not loaded yet, please wait"}), 503

        if 'file' not in request.files:
            return jsonify({"error": "No audio file provided"}), 400

        audio_file = request.files['file']
        if audio_file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        print(f"Processing file: {audio_file.filename}")

        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
            tmp_file_path = tmp_file.name
            audio_file.save(tmp_file_path)
            print(f"Saved temp file: {tmp_file_path}")

            # Audio file is ready for processing
            print(f"Audio file ready for transcription")

            # Clear GPU cache before processing
            if device == "cuda":
                torch.cuda.empty_cache()

            # Transcribe with faster-whisper (GPU accelerated)
            print(f"Starting transcription with faster-whisper")

            try:
                segments, info = model.transcribe(tmp_file_path, beam_size=5)
                print(f"Detected language: {info.language} (probability: {info.language_probability:.2f})")

                # Convert segments to list and extract text
                segments_list = []
                full_text = ""

                for segment in segments:
                    segment_dict = {
                        "start": segment.start,
                        "end": segment.end,
                        "text": segment.text
                    }
                    segments_list.append(segment_dict)
                    full_text += segment.text + " "

                print(f"Transcription completed, found {len(segments_list)} segments")

                # Clear GPU cache after transcription
                if device == "cuda":
                    torch.cuda.empty_cache()

            except torch.cuda.OutOfMemoryError as e:
                print(f"GPU out of memory error: {e}")
                print("Attempting CPU fallback...")
                try:
                    # Try with CPU fallback
                    cpu_model = WhisperModel(os.getenv("WHISPERX_MODEL", "base"), device="cpu", compute_type="int8")
                    segments, info = cpu_model.transcribe(tmp_file_path, beam_size=5)
                    print(f"CPU fallback successful! Detected language: {info.language}")

                    # Convert segments to list and extract text
                    segments_list = []
                    full_text = ""

                    for segment in segments:
                        segment_dict = {
                            "start": segment.start,
                            "end": segment.end,
                            "text": segment.text
                        }
                        segments_list.append(segment_dict)
                        full_text += segment.text + " "

                    print(f"CPU transcription completed, found {len(segments_list)} segments")
                except Exception as cpu_error:
                    print(f"CPU fallback also failed: {cpu_error}")
                    return jsonify({"error": "Both GPU and CPU transcription failed"}), 500
                finally:
                    if device == "cuda":
                        torch.cuda.empty_cache()

            except Exception as e:
                print(f"Error during transcription: {e}")
                if device == "cuda":
                    torch.cuda.empty_cache()
                return jsonify({"error": f"Transcription failed: {str(e)}"}), 500

            # Format response similar to OpenAI Whisper API
            response = {
                "text": full_text.strip(),
                "segments": segments_list,
                "language": info.language
            }

            print(f"Transcription successful: {len(full_text)} characters")
            return jsonify(response)

    except Exception as e:
        print(f"Error during transcription: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500
    finally:
        # Clean up temp file
        if tmp_file_path and os.path.exists(tmp_file_path):
            try:
                os.unlink(tmp_file_path)
                print(f"Cleaned up temp file: {tmp_file_path}")
            except Exception as e:
                print(f"Error cleaning up temp file: {e}")

@app.route("/transcribe", methods=["POST"])
def transcribe_alt():
    # Alternative endpoint for compatibility
    return transcribe()

@app.route("/audio/transcriptions", methods=["POST"])
def transcribe_openwebui():
    # OpenWebUI compatibility endpoint (without /v1 prefix)
    return transcribe()

if __name__ == "__main__":
    print("Starting WhisperX server...")
    if load_models():
        print("All models loaded successfully, starting server...")
        port = int(os.getenv("PORT", "9000"))
        app.run(host="0.0.0.0", port=port, debug=False)
    else:
        print("Failed to load models, exiting...")
        exit(1)
EOF

# Set permissions
RUN chmod +x /app/whisperx_server.py

# Expose port
EXPOSE 9000

# Health check
HEALTHCHECK --interval=30s --timeout=15s --start-period=90s --retries=3 \
    CMD curl -f http://localhost:9000/health || exit 1

# Start the server
CMD ["python3", "/app/whisperx_server.py"]
