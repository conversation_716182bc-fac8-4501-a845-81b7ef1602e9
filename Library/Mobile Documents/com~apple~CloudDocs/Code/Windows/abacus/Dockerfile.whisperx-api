FROM nvidia/cuda:11.8-runtime-ubuntu20.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    git \
    curl \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Install Python dependencies with GPU support
RUN pip3 install --no-cache-dir \
    torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118 \
    git+https://github.com/m-bain/whisperX.git \
    flask \
    flask-cors \
    requests

# Copy the API server script
COPY <<EOF /app/whisperx_api_server.py
import os
import tempfile
import whisperx
import torch
from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# Global variables for models
device = "cuda" if torch.cuda.is_available() else "cpu"
compute_type = "float16" if device == "cuda" else "int8"
print(f"Using device: {device}")
model = None
align_model = None
metadata = None

def load_models():
    global model, align_model, metadata

    try:
        # Load WhisperX model
        model_name = os.getenv("WHISPERX_MODEL", "base")
        print(f"Loading WhisperX model: {model_name}")
        model = whisperx.load_model(model_name, device, compute_type=compute_type)

        # Load alignment model for English
        print("Loading alignment model...")
        align_model, metadata = whisperx.load_align_model(language_code="en", device=device)

        print("Models loaded successfully!")
        return True
    except Exception as e:
        print(f"Error loading models: {e}")
        return False

@app.route("/", methods=["GET"])
def health_check():
    return jsonify({"status": "healthy", "service": "WhisperX API Server"})

@app.route("/health", methods=["GET"])
def health():
    return jsonify({"status": "healthy", "service": "WhisperX API Server"})

@app.route("/v1/audio/transcriptions", methods=["POST"])
def transcribe():
    tmp_file_path = None
    try:
        print(f"Received transcription request from {request.remote_addr}")

        # Check if models are loaded
        if model is None:
            return jsonify({"error": "Models not loaded yet, please wait"}), 503

        if 'file' not in request.files:
            return jsonify({"error": "No audio file provided"}), 400

        audio_file = request.files['file']
        if audio_file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        print(f"Processing file: {audio_file.filename}")

        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
            tmp_file_path = tmp_file.name
            audio_file.save(tmp_file_path)
            print(f"Saved temp file: {tmp_file_path}")

            # Load audio with WhisperX
            try:
                audio = whisperx.load_audio(tmp_file_path)
                print(f"Audio loaded successfully")
            except Exception as e:
                print(f"Error loading audio: {e}")
                return jsonify({"error": f"Failed to load audio: {str(e)}"}), 400

            # Transcribe with WhisperX
            batch_size = int(os.getenv("WHISPERX_BATCH_SIZE", "16"))
            print(f"Starting transcription with WhisperX, batch_size: {batch_size}")

            result = model.transcribe(audio, batch_size=batch_size)
            print(f"Transcription completed, found {len(result.get('segments', []))} segments")

            # Align whisper output for better timestamps
            if result.get("segments") and align_model:
                print("Starting alignment...")
                result = whisperx.align(result["segments"], align_model, metadata, audio, device, return_char_alignments=False)
                print("Alignment completed")

            # Extract text and segments
            segments_list = result.get("segments", [])
            full_text = " ".join([segment["text"] for segment in segments_list])

            # Format response similar to OpenAI Whisper API
            response = {
                "text": full_text.strip(),
                "segments": segments_list,
                "language": result.get("language", "en")
            }

            print(f"Transcription successful: {len(full_text)} characters")
            return jsonify(response)

    except Exception as e:
        print(f"Error during transcription: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500
    finally:
        # Clean up temp file
        if tmp_file_path and os.path.exists(tmp_file_path):
            try:
                os.unlink(tmp_file_path)
                print(f"Cleaned up temp file: {tmp_file_path}")
            except Exception as e:
                print(f"Error cleaning up temp file: {e}")

@app.route("/transcribe", methods=["POST"])
def transcribe_alt():
    # Alternative endpoint for compatibility
    return transcribe()

@app.route("/audio/transcriptions", methods=["POST"])
def transcribe_openwebui():
    # OpenWebUI compatibility endpoint
    return transcribe()

if __name__ == "__main__":
    print("Starting WhisperX API Server...")
    if load_models():
        print("All models loaded successfully, starting server...")
        port = int(os.getenv("PORT", "9000"))
        app.run(host="0.0.0.0", port=port, debug=False)
    else:
        print("Failed to load models, exiting...")
        exit(1)
EOF

# Set permissions
RUN chmod +x /app/whisperx_api_server.py

# Expose port
EXPOSE 9000

# Health check
HEALTHCHECK --interval=30s --timeout=15s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:9000/health || exit 1

# Start the server
CMD ["python3", "/app/whisperx_api_server.py"]
