# ComfyUI and AppAgent Setup Guide

This guide explains how to use the newly added ComfyUI and AppAgent services in your Docker Compose setup.

## Services Added

### 1. ComfyUI - Stable Diffusion Workflows
- **Port**: 8188
- **Image**: `yanwk/comfyui-boot:cu124-megapak`
- **Purpose**: Node-based workflow interface for Stable Diffusion and other AI image generation models
- **GPU**: Requires NVIDIA GPU with CUDA support

#### ComfyUI Features:
- Web-based interface for creating AI image generation workflows
- Support for multiple models (Stable Diffusion, FLUX, etc.)
- Custom nodes and extensions
- Batch processing capabilities

#### ComfyUI Data Directories:
- `C:\Users\<USER>\Nextcloud3\Documents\ComfyUI\Models` - Your main AI models directory
- `C:\Users\<USER>\Nextcloud3\Documents\ComfyUI\loras` - LoRA models for easy access
- `C:\Users\<USER>\Nextcloud3\Documents\ComfyUI\controlnet` - ControlNet models for easy access
- `./data/comfyui/output` - Generated images will be saved here
- `./data/comfyui/input` - Place input images here
- `./data/comfyui/custom_nodes` - Custom extensions and nodes

### 2. AppAgent - Smartphone Automation
- **Port**: 8189
- **Purpose**: LLM-based multimodal agent for automating smartphone applications
- **Requirements**: Android device with USB debugging enabled

#### AppAgent Features:
- Autonomous exploration of Android apps
- Learning from human demonstrations
- Task automation on smartphones
- GPT-4V or Qwen-VL powered decision making

## Setup Instructions

### Prerequisites
1. **NVIDIA GPU** with CUDA support for ComfyUI
2. **Android device** with USB debugging enabled for AppAgent
3. **OpenAI API key** for AppAgent (or Dashscope API key for Qwen)

### Environment Variables
Add these to your `.env` file:

```bash
# For AppAgent
OPENAI_API_KEY=sk-your-openai-api-key-here
DASHSCOPE_API_KEY=sk-your-dashscope-key-here  # Optional, for Qwen model
```

### Starting the Services

1. **Start ComfyUI**:
   ```bash
   docker-compose up -d comfyui
   ```
   Access at: http://localhost:8188

2. **Start AppAgent**:
   ```bash
   docker-compose up -d appagent
   ```
   Access at: http://localhost:8189

3. **Start both services**:
   ```bash
   docker-compose up -d comfyui appagent
   ```

## Usage

### ComfyUI Usage
1. Open http://localhost:8188 in your browser
2. Place your models in the appropriate directories:
   - Main models: `C:\Users\<USER>\Nextcloud3\Documents\ComfyUI\Models\`
   - LoRA models: `C:\Users\<USER>\Nextcloud3\Documents\ComfyUI\loras\`
   - ControlNet models: `C:\Users\<USER>\Nextcloud3\Documents\ComfyUI\controlnet\`
3. Create workflows using the node-based interface
4. Generated images will appear in `./data/comfyui/output/`

### AppAgent Usage
1. Connect your Android device via USB
2. Enable USB debugging in Developer Options
3. Access the web interface at http://localhost:8189
4. Use the command line interface inside the container:
   ```bash
   # Enter the container
   docker exec -it appagent bash
   
   # Learning phase (autonomous exploration or human demonstration)
   python learn.py
   
   # Deployment phase (execute tasks)
   python run.py
   ```

## Configuration

### ComfyUI Configuration
- Models should be placed in `C:\Users\<USER>\Nextcloud3\Documents\ComfyUI\`
- Custom nodes can be added to `./data/comfyui/custom_nodes/`
- Configuration persists in `./data/comfyui/` directory
- Models are shared with your Nextcloud3 sync for easy access across devices

### AppAgent Configuration
- Edit `repos/AppAgent/config.yaml` to modify settings
- Change MODEL from "OpenAI" to "Qwen" to use Qwen-VL instead of GPT-4V
- Adjust REQUEST_INTERVAL to control API call frequency

## Troubleshooting

### ComfyUI Issues
- **GPU not detected**: Ensure NVIDIA Docker runtime is installed
- **Models not loading**: Check `C:\Users\<USER>\Nextcloud3\Documents\ComfyUI\` directory permissions and ensure models are in correct subdirectories
- **Out of memory**: Reduce batch size or use smaller models

### AppAgent Issues
- **Device not found**: Check USB debugging is enabled and device is connected
- **API errors**: Verify your OpenAI API key is valid and has sufficient credits
- **Permission denied**: Ensure the container has USB device access

## Resource Requirements

### ComfyUI
- **CPU**: 2-6 cores
- **Memory**: 8-16GB RAM
- **GPU**: NVIDIA GPU with 8GB+ VRAM recommended
- **Storage**: 20GB+ for models

### AppAgent
- **CPU**: 0.5-2 cores
- **Memory**: 1-4GB RAM
- **Storage**: 1GB for documentation and screenshots

## Security Notes
- AppAgent runs in privileged mode for USB access
- Both services have security optimizations applied
- Logs are rotated to prevent disk space issues
- Temporary filesystems are used for /tmp directories
