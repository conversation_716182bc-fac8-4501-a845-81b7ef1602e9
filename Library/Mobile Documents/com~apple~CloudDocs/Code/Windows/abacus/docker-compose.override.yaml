services:
  librechat:
    environment:
      # Database connections (essential for startup)
      - MONGO_URI=mongodb://abacus_chat_mongodb:27017/LibreChat
      - MEILI_HOST=http://abacus_chat_meilisearch:7700
      # Minimal configuration for testing
      - ENDPOINTS=custom
      - DISABLE_OPENAI=true
      # Disable all external API keys for testing
      - OPENAI_API_KEY=
      - GOOGLE_KEY=
      - OPENROUTER_KEY=
