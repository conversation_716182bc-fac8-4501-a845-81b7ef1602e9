version: 1.1.4
cache: true

interface:
  customWelcome: "Welcome to Abacus! Enjoy your experience."
  agents: true # Explicitly enable agents

endpoints:
  agents:
    recursionLimit: 100
    maxRecursionLimit: 150
    capabilities:
      - "execute_code"
      - "file_search"
      - "actions"
      - "tools"
      - "artifacts"
      - "ocr"
      - "chain"

  custom:
    - name: "OpenRouter"
      apiKey: "${OPENROUTER_KEY}"
      baseURL: "https://openrouter.ai/api/v1"
      models:
        default: ["meta-llama/llama-3-70b-instruct"]
        fetch: true
      titleConvo: true
      titleModel: "meta-llama/llama-3-70b-instruct"
      # Recommended: Drop the stop parameter from the request as Openrouter models use a variety of stop tokens.
      dropParams: ["stop"]
      modelDisplayLabel: "OpenRouter"
    - name: "Ollama"
      apiKey: "ollama"
      baseURL: "http://host.docker.internal:11434/v1/"
      models:
        default: [
          "llama3:latest",
          "command-r",
          "mixtral",
          "phi3"
          ]
        fetch: true # fetching list of models is supported
      titleConvo: true
      titleModel: "current_model"
      modelDisplayLabel: "Ollama"

mcpServers:
  desktop-commander:
    command: docker
    args:
      - "run"
      - "-i"
      - "--rm"
      - "mcp/desktop-commander"

  searxng:
    type: http
    url: http://searxng-mcp:5000/search
    method: POST
    body:
      query: "{{query}}"
    headers:
      Content-Type: "application/json"
