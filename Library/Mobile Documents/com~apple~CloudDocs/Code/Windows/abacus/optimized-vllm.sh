#!/bin/bash

# Set environment variables to control GPU usage
export CUDA_DEVICE_ORDER=PCI_BUS_ID
# Use only the first GPU (index 0)
export CUDA_VISIBLE_DEVICES=0

# Set Hugging Face cache directory to the mounted volume
export HF_HOME=/data/huggingface
export TRANSFORMERS_CACHE=/data/huggingface/transformers
export HUGGINGFACE_HUB_CACHE=/data/huggingface/hub

# Create cache directories if they don't exist
mkdir -p /data/huggingface/transformers
mkdir -p /data/huggingface/hub
mkdir -p /data/models

# Run vLLM serve with the 4B model and maximum stability settings
# These settings prioritize absolute stability for RTX 3090 (24GB VRAM)
# Even 55.3% GPU memory utilization caused system freezing
# Maximum stability parameters:
# - gpu-memory-utilization: 0.55 (55%) - Slightly LOWER than the 55.25% in start-vllm.sh
# - All other parameters kept exactly the same as the known stable configuration
#
# IMPORTANT: Your system is extremely sensitive to these settings.
# If you want to experiment further, try increasing ONE parameter at a time by a tiny amount:
# - Increase gpu-memory-utilization by 0.001 (0.1%) at a time
# - OR increase max-model-len by 2-4 tokens at a time
# - OR increase max-num-batched-tokens by 2-4 tokens at a time
# Command parameters explained:
# --dtype float16                     # force half-precision to cut VRAM in half
# --gpu-memory-utilization 0.55       # drop from .65 → .55 to leave headroom
# --swap-space 8                      # allow 8 GiB CPU spill instead of 4
# --max-model-len 1024                # shorten context from 1536 → 1024 tokens
# --max-num-seqs 2                    # only 2 concurrent sequences
# --max-num-batched-tokens 512        # shrink token batch to 512
# --quantization q4_k_m               # lighter Q4_K_M quantization vs. AWQ_Marlin
# --prefill-partition 0.5             # split GPU cache evenly between prefill & decode

# Single line command without inline comments to avoid shell parsing issues
# Using OpenAI-compatible API server for LibreChat integration
# Added --api-key sk-dummy to accept any API key
vllm serve Orion-zhen/Qwen3-14B-AWQ --dtype float16 --gpu-memory-utilization 0.9 --swap-space 8 --max-model-len 4096 --max-num-seqs 2 --max-num-batched-tokens 512 --tensor-parallel-size 1 --block-size 16 --max-logprobs 5 --host 0.0.0.0 --port 7860 --served-model-name Qwen3-14B-AWQ --api-key sk-dummy
