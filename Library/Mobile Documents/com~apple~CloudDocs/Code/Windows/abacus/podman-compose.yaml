networks:
  abacus-network:
    external: true

services:
  open-webui:
    image: ghcr.io/open-webui/open-webui:dev-cuda
    container_name: open_webui # Changed to valid name
    ports:
      - "3000:8080"
    environment:
      - ENABLE_WEB_SEARCH=true
      - WEB_SEARCH_ENGINE=searxng
      - WEB_SEARCH_RESULT_COUNT=5
      - WEB_SEARCH_CONCURRENT_REQUESTS=10
      - SEARXNG_QUERY_URL=http://searxng:8080/search
    volumes:
      - ./data/open_webui:/app/backend/data # Relative path for persistent data
      # Removed problematic Obsidian volume mount
    restart: always # Changed from unless-stopped based on previous setup
    deploy: # For GPU support
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    depends_on:
      - searxng # Ensure searxng starts first
    networks:
      - abacus-network

  app:
    image: itzcrazykns1337/perplexica:main
    container_name: perplexica_search # Changed back to valid name
    ports:
      - "3001:3000" # Port from docker run command
    environment:
      - SEARXNG_API_URL=http://searxng:8080
    volumes:
      - backend-dbstore:/home/<USER>/data # Named volume for DB
      - uploads:/home/<USER>/uploads # Named volume for uploads
      - ./data/perplexica/config.toml:/home/<USER>/config.toml # Relative path for config
    restart: unless-stopped
    depends_on:
      - searxng # Ensure searxng starts first
    networks:
      - abacus-network

  searxng:
    image: docker.io/searxng/searxng:latest
    container_name: searxng
    ports:
      - "4000:8080"
    volumes:
      - ./data/searxng:/etc/searxng # Relative path for config
    restart: unless-stopped
    networks:
      - abacus-network

  n8n:
    image: n8nio/n8n:next
    container_name: n8n
    ports:
      - "5678:5678"
    environment:
      - NODE_ENV=production
    volumes:
      - ./data/n8n:/home/<USER>/.n8n # Relative path for n8n data
      - ./data/mcp/perplexica:/mcp-servers/perplexica # Relative path for MCP server
    restart: unless-stopped
    networks:
      - abacus-network

  # --- Supabase Services have been moved to supabase-compose.yaml ---

  # --- LibreChat Services Start ---

  librechat:
    image: ghcr.io/danny-avila/librechat:latest
    container_name: librechat
    ports:
      - "3080:3080"
    env_file:
      - ./repos/LibreChat/.env
    environment:
      - MONGO_URI=mongodb://mongodb:27017/LibreChat
      - MEILI_HOST=http://meilisearch:7700
      - RAG_API_URL=http://rag_api:8000
    depends_on:
      - mongodb
      - meilisearch
      - rag_api
    volumes:
      - ./repos/LibreChat/uploads:/app/uploads
      - ./repos/LibreChat/logs:/app/logs
      - ./librechat.yaml:/app/librechat.yaml
    networks:
      - abacus-network

  mongodb:
    container_name: mongodb
    image: mongo
    restart: always
    volumes:
      - ./repos/LibreChat/data-node:/data/db
    command: mongod --noauth
    networks:
      - abacus-network

  meilisearch:
    container_name: meilisearch
    env_file:
      - ./repos/LibreChat/.env
    image: getmeili/meilisearch:v1.12.3
    restart: always
    environment:
      - MEILI_HOST=http://meilisearch:7700
      - MEILI_NO_ANALYTICS=true
      - MEILI_MASTER_KEY=${MEILI_MASTER_KEY}
    volumes:
      - ./repos/LibreChat/meili_data_v1.12:/meili_data
    networks:
      - abacus-network

  vectordb:
    container_name: vectordb
    image: ankane/pgvector:latest
    environment:
      POSTGRES_DB: mydatabase
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: mypassword
    restart: always
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - abacus-network

  rag_api:
    container_name: rag_api
    image: ghcr.io/danny-avila/librechat-rag-api-dev-lite:latest
    environment:
      - DB_HOST=vectordb
      - RAG_PORT=${RAG_PORT:-8000}
    depends_on:
      - vectordb
    env_file:
      - ./repos/LibreChat/.env
    networks:
      - abacus-network

  # --- Whisper Service for Audio Transcription ---
  whisper:
    image: onerahmet/openai-whisper-asr-webservice:latest-gpu
    container_name: whisper
    # Expose port 9000 directly
    ports:
      - "9000:9000"
    environment:
      - ASR_MODEL=base
      - ASR_ENGINE=faster_whisper
      - COMPUTE_TYPE=float16
      - DEVICE=cuda
    volumes:
      - ./data/whisper:/root/.cache/whisper
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - abacus-network

  # --- MCPO Service for MCP Servers ---
  mcpo:
    image: ghcr.io/open-webui/mcpo:main
    container_name: mcpo
    ports:
      - "8000:8000"
    volumes:
      - ./mcpo/config/config.json:/app/config.json
    environment:
      - BRAVE_API_KEY=BSAWY7PYoifzbn5clZU6YSFKXl3ANvM
      - CLEAR_THOUGHT_API_KEY=775421fd-e657-48bf-adaf-b8abc705641c
    command: >
      --api-key "mcpo_api_key_123456789" --config /app/config.json --server-type "streamable_http"
    restart: unless-stopped
    networks:
      - abacus-network

  flowise:
    image: flowiseai/flowise
    restart: always
    environment:
      - PORT=3002
      - DATABASE_PATH=/root/.flowise
      - SECRETKEY_PATH=/root/.flowise
      - LOG_PATH=/root/.flowise/logs
      - BLOB_STORAGE_PATH=/root/.flowise/storage
      - CORS_ORIGINS=${CORS_ORIGINS}
      - IFRAME_ORIGINS=${IFRAME_ORIGINS}
      - FLOWISE_FILE_SIZE_LIMIT=${FLOWISE_FILE_SIZE_LIMIT}
      - DEBUG=${DEBUG}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_PORT=${DATABASE_PORT}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_NAME=${DATABASE_NAME}
      - DATABASE_USER=${DATABASE_USER}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SSL=${DATABASE_SSL}
      - DATABASE_SSL_KEY_BASE64=${DATABASE_SSL_KEY_BASE64}
      - FLOWISE_SECRETKEY_OVERWRITE=${FLOWISE_SECRETKEY_OVERWRITE}
      - SECRETKEY_STORAGE_TYPE=${SECRETKEY_STORAGE_TYPE}
      - SECRETKEY_AWS_ACCESS_KEY=${SECRETKEY_AWS_ACCESS_KEY}
      - SECRETKEY_AWS_SECRET_KEY=${SECRETKEY_AWS_SECRET_KEY}
      - SECRETKEY_AWS_REGION=${SECRETKEY_AWS_REGION}
      - SECRETKEY_AWS_NAME=${SECRETKEY_AWS_NAME}
      - LOG_LEVEL=${LOG_LEVEL}
      - MODEL_LIST_CONFIG_JSON=${MODEL_LIST_CONFIG_JSON}
      - GLOBAL_AGENT_HTTP_PROXY=${GLOBAL_AGENT_HTTP_PROXY}
      - GLOBAL_AGENT_HTTPS_PROXY=${GLOBAL_AGENT_HTTPS_PROXY}
      - GLOBAL_AGENT_NO_PROXY=${GLOBAL_AGENT_NO_PROXY}
      - DISABLED_NODES=${DISABLED_NODES}
      - MODE=${MODE}
      - WORKER_CONCURRENCY=${WORKER_CONCURRENCY}
      - QUEUE_NAME=${QUEUE_NAME}
      - QUEUE_REDIS_EVENT_STREAM_MAX_LEN=${QUEUE_REDIS_EVENT_STREAM_MAX_LEN}
      - REMOVE_ON_AGE=${REMOVE_ON_AGE}
      - REMOVE_ON_COUNT=${REMOVE_ON_COUNT}
      - REDIS_URL=${REDIS_URL}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_USERNAME=${REDIS_USERNAME}
      - REDIS_TLS=${REDIS_TLS}
      - REDIS_CERT=${REDIS_CERT}
      - REDIS_KEY=${REDIS_KEY}
      - REDIS_CA=${REDIS_CA}
      - REDIS_KEEP_ALIVE=${REDIS_KEEP_ALIVE}
      - ENABLE_BULLMQ_DASHBOARD=${ENABLE_BULLMQ_DASHBOARD}
    ports:
      - '3002:3002'
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3002/api/v1/ping']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    volumes:
      - ./data/flowise:/root/.flowise
    entrypoint: /bin/sh -c "sleep 3; flowise start"
    networks:
      - abacus-network

  crawl4ai:
    image: unclecode/crawl4ai:latest
    container_name: crawl4ai
    ports:
      - "11235:11235"
    env_file:
      - ./repos/crawl4ai/.llm.env
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - GROQ_API_KEY=${GROQ_API_KEY:-}
      - TOGETHER_API_KEY=${TOGETHER_API_KEY:-}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY:-}
      - GEMINI_API_TOKEN=${GEMINI_API_TOKEN:-}
    volumes:
      - /dev/shm:/dev/shm
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 1G
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11235/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    user: "appuser"
    networks:
      - abacus-network

  agent-zero:
    image: frdel/agent-zero-run
    container_name: agent-zero
    restart: unless-stopped
    ports:
      - "50001:80"
    volumes:
      - ./data/agent-zero:/a0
    env_file:
      - ./data/agent-zero/.env
    environment:
      - PROMPT_DIR=/a0/prompts # Explicitly set the prompt directory
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all # Use all available GPUs
              capabilities: [gpu, compute, utility] # Ensure all necessary capabilities
    networks:
      - abacus-network

  perplexica-mcp:
    build:
      context: ./mcp_servers/perplexica
      dockerfile: Dockerfile
    container_name: perplexica-mcp
    ports:
      - "8008:8008"
    restart: unless-stopped
    networks:
      - abacus-network

  searxng-mcp:
    build:
      context: ./mcp_servers/searxng
      dockerfile: Dockerfile
    container_name: searxng-mcp
    ports:
      - "5001:5000"
    environment:
      - SEARXNG_URL=http://searxng:8080
    restart: unless-stopped
    depends_on:
      - searxng
    networks:
      - abacus-network

volumes:
  backend-dbstore: # Define named volume for perplexica DB
  uploads: # Define named volume for perplexica uploads
  # Note: The open-webui volume is now a bind mount, not a named volume here.
  db-config: # Supabase volume
  pgdata: # Volume for vectordb
  librechat_pgdata: # Additional volume (keep for compatibility)
  whisper_data: # Volume for whisper model cache
  flowise_data: # Volume for Flowise data

  desktop-commander-mcpo:
    build:
      context: ./mcp_servers/desktop-commander
      dockerfile: Dockerfile
    container_name: desktop-commander-mcpo
    ports:
      - "8001:8000" # Expose on a different port to avoid conflict with main mcpo
    volumes:
      - D:/:/host_d_drive
    restart: unless-stopped
    networks:
      - abacus-network
