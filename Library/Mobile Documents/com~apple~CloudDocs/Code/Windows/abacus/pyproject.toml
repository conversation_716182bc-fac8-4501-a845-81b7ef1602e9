[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mcp-obsidian-server"
version = "0.1.0"
description = "MCP server for Obsidian integration with n8n"
readme = "README.md"
authors = [
    {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"}
]
license = {text = "MIT"}
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
requires-python = ">=3.8"
dependencies = [
    "mcp>=0.1.0",
    "requests>=2.25.0",
    "python-dotenv>=0.15.0",
    "uvicorn>=0.15.0",
    "starlette>=0.17.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "black>=21.5b2",
    "isort>=5.9.1",
    "mypy>=0.812",
    "flake8>=3.9.2",
]

[project.scripts]
mcp-obsidian-server = "main:main"

[tool.setuptools]
packages = ["src.mcp_obsidian_server"]

[tool.black]
line-length = 88
target-version = ["py38"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
