@echo off
echo Stopping all services...
docker-compose -p abacus -f docker-compose.yaml down
docker-compose -p supabase -f supabase-compose.yaml down

echo Removing abacus-network if it exists...
docker network rm abacus-network 2>nul || echo Network does not exist or could not be removed.

echo Creating abacus-network...
docker network create abacus-network

echo Starting Supabase services...
docker-compose -p supabase -f supabase-compose.yaml up -d

echo Waiting for Supabase to initialize (30 seconds)...
timeout /t 30

echo Starting main services...
docker-compose -p abacus -f docker-compose.yaml up -d

echo All services have been restarted.
echo.
echo If you encounter any issues, try running:
echo docker network inspect abacus-network
echo to verify the network was created correctly.
