#!/bin/bash

# Set environment variables to control GPU usage
export CUDA_DEVICE_ORDER=PCI_BUS_ID
# Use only the first GPU (index 0)
export CUDA_VISIBLE_DEVICES=0

# Set Hugging Face cache directory to the mounted volume
export HF_HOME=/data/huggingface
export TRANSFORMERS_CACHE=/data/huggingface/transformers
export HUGGINGFACE_HUB_CACHE=/data/huggingface/hub

# Create cache directories if they don't exist
mkdir -p /data/huggingface/transformers
mkdir -p /data/huggingface/hub
mkdir -p /data/models

# Run vLLM serve with the UI-TARS-1.5-7B model
# These settings are adjusted for the larger 7B model and multimodal capabilities.
vllm serve \
  ByteDance-Seed/UI-TARS-1.5-7B \
  --served-model-name UI-TARS-1.5-7B \
  --dtype float16 \
  --gpu-memory-utilization 0.9 \
  --max-model-len 1536 \
  --max-num-seqs 16 \
  --max-num-batched-tokens 8192 \
  --tensor-parallel-size 1 \
  --block-size 32 \
  --max-logprobs 5 \
  --host 0.0.0.0 \
  --port 7860 \
