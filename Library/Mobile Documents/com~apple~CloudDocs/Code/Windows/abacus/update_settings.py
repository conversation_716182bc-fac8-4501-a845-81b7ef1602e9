import json

# The original settings content provided by the user
settings_content_string = """
{
    "chat_model_provider": "GOOGLE",
    "chat_model_name": "gemini-2.5-flash-preview-05-20",
    "chat_model_kwargs": {
        "temperature": "0"
    },
    "chat_model_ctx_length": 1000000,
    "chat_model_ctx_history": 0.7,
    "chat_model_vision": true,
    "chat_model_rl_requests": 0,
    "chat_model_rl_input": 0,
    "chat_model_rl_output": 0,
    "util_model_provider": "GOOGLE",
    "util_model_name": "gemini-2.5-flash-preview-05-20",
    "util_model_ctx_length": 100000,
    "util_model_ctx_input": 0.7,
    "util_model_kwargs": {
        "temperature": "0"
    },
    "util_model_rl_requests": 0,
    "util_model_rl_input": 0,
    "util_model_rl_output": 0,
    "embed_model_provider": "HUGGINGFACE",
    "embed_model_name": "sentence-transformers/all-MiniLM-L6-v2",
    "embed_model_kwargs": {},
    "embed_model_rl_requests": 0,
    "embed_model_rl_input": 0,
    "embed_model_rl_output": 0,
    "browser_model_provider": "GOOGLE",
    "browser_model_name": "gemini-2.5-flash-preview-05-20",
    "browser_model_vision": true,
    "browser_model_kwargs": {
        "temperature": "0"
    },
    "api_keys": {},
    "auth_login": "",
    "auth_password": "",
    "root_password": "",
    "agent_prompts_subdir": "abacus_orchestrator",
    "agent_memory_subdir": "default",
    "agent_knowledge_subdir": "custom",
    "rfc_auto_docker": true,
    "rfc_url": "localhost",
    "rfc_password": "",
    "rfc_port_http": 55080,
    "rfc_port_ssh": 55022,
    "stt_model_size": "turbo",
    "stt_language": "en",
    "stt_silence_threshold": 0.3,
    "stt_silence_duration": 1000,
    "stt_waiting_timeout": 2000,
    "mcp_servers": "{\\n  \\\"mcpServers\\\": {\\n    \\\"sqlite\\\": {\\n      \\\"command\\\": \\\"uvx\\\",\\n      \\\"args\\\": [\\n        \\\"mcp-server-sqlite\\\",\\n        \\\"--db-path\\\",\\n        \\\"/root/db.sqlite\\\"\\n      ],\\n      \\\"init_timeout\\\": 10,\\n      \\\"tool_timeout\\\": 200\\n    },\\n    \\\"sequential-thinking\\\": {\\n      \\\"disabled\\\": true,\\n      \\\"command\\\": \\\"npx\\\",\\n      \\\"args\\\": [\\n        \\\"--yes\\\",\\n        \\\"--package\\\",\\n        \\\"@modelcontextprotocol/server-sequential-thinking\\\",\\n        \\\"mcp-server-sequential-thinking\\\"\\n      ]\\n    },\\n    \\\"deep-wiki\\\": {\\n      \\\"description\\\": \\\"Use this MCP to analyze github repositories\\\",\\n      \\\"url\\\": \\\"https://mcp.deepwiki.com/sse\\\"\\n    }\\n  }\\n}",
    "mcp_client_init_timeout": 5,
    "mcp_client_tool_timeout": 120,
    "mcp_server_enabled": true,
    "mcp_server_token": ""
}
"""

# Step 1: Parse the main settings JSON string
settings_data = json.loads(settings_content_string)

# Step 2: Extract the mcp_servers value, which is a string containing another JSON object
mcp_servers_string = settings_data.get('mcp_servers', '{}')

# Step 3: Parse the inner JSON string
mcp_servers_data = json.loads(mcp_servers_string)

# Step 4: Add or update the Abacus Agent configuration with the corrected URL
if 'mcpServers' not in mcp_servers_data:
    mcp_servers_data['mcpServers'] = {}

abacus_agent_config = {
    "name": "abacus-agent",
    "description": "Abacus Agent Orchestrator (OpenAI-compatible API endpoint)",
    "url": "http://host.docker.internal:8000/tools",
    "disabled": False
}
mcp_servers_data['mcpServers']['abacus-agent'] = abacus_agent_config

# Step 5: Convert the modified mcp_servers dictionary back into a JSON string
updated_mcp_servers_string = json.dumps(mcp_servers_data, indent=2)

# Step 6: Place the newly created JSON string back into the main settings dictionary
settings_data['mcp_servers'] = updated_mcp_servers_string

# Step 7: Convert the final, updated settings dictionary into a formatted JSON string for output
final_settings_json = json.dumps(settings_data, indent=4)

print(final_settings_json)
